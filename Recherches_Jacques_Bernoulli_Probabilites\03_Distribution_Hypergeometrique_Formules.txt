DISTRIBUTION HYPERGÉOMÉTRIQUE - FORMULES ET APPLICATIONS
======================================================

DÉFINITION FONDAMENTALE
-----------------------
La distribution hypergéométrique modélise la probabilité d'obtenir un certain nombre de succès dans un échantillon tiré d'une population finie SANS REMISE.

FORMULE PRINCIPALE
------------------
P(X = k) = [C(K,k) × C(N-K,n-k)] / C(N,n)

Où :
- N = taille totale de la population
- K = nombre total de "succès" dans la population
- n = taille de l'échantillon tiré
- k = nombre de succès désirés dans l'échantillon
- C(a,b) = combinaison "a choisir b" = a! / (b!(a-b)!)

DÉRIVATION COMBINATOIRE DÉTAILLÉE
=================================

ÉTAPE 1 : COMPTER LES RÉSULTATS FAVORABLES
------------------------------------------
Nombre de façons de choisir k succès parmi K disponibles : C(K,k)
Nombre de façons de choisir (n-k) échecs parmi (N-K) disponibles : C(N-K,n-k)
Nombre total de résultats favorables : C(K,k) × C(N-K,n-k)

ÉTAPE 2 : COMPTER TOUS LES RÉSULTATS POSSIBLES
----------------------------------------------
Nombre total de façons de choisir n éléments parmi N : C(N,n)

ÉTAPE 3 : CALCUL DE LA PROBABILITÉ
----------------------------------
P(X = k) = (Résultats favorables) / (Résultats possibles totaux)
P(X = k) = [C(K,k) × C(N-K,n-k)] / C(N,n)

DÉVELOPPEMENT EN FACTORIELLES
=============================

C(K,k) = K! / (k!(K-k)!)
C(N-K,n-k) = (N-K)! / ((n-k)!(N-K-n+k)!)
C(N,n) = N! / (n!(N-n)!)

Donc :
P(X = k) = [K! × (N-K)! × n! × (N-n)!] / [k! × (K-k)! × (n-k)! × (N-K-n+k)! × N!]

PARAMÈTRES ET CONTRAINTES
=========================

DOMAINE DE DÉFINITION
---------------------
- N ∈ {0, 1, 2, ...} (entiers positifs)
- K ∈ {0, 1, 2, ..., N}
- n ∈ {0, 1, 2, ..., N}
- k ∈ {max(0, n+K-N), ..., min(n, K)}

CONTRAINTES LOGIQUES
-------------------
- k ≤ n (ne peut pas avoir plus de succès que d'éléments tirés)
- k ≤ K (ne peut pas avoir plus de succès que disponibles)
- (n-k) ≤ (N-K) (ne peut pas avoir plus d'échecs que disponibles)
- k ≥ max(0, n+K-N) (contrainte de borne inférieure)

PROPRIÉTÉS STATISTIQUES
=======================

ESPÉRANCE MATHÉMATIQUE
----------------------
E[X] = μ = n × (K/N)

Interprétation : L'espérance est proportionnelle à la fraction de succès dans la population.

VARIANCE
--------
Var(X) = σ² = n × (K/N) × (1-K/N) × (N-n)/(N-1)

Le facteur (N-n)/(N-1) est appelé "facteur de correction de population finie"

ÉCART-TYPE
----------
σ = √[n × (K/N) × (1-K/N) × (N-n)/(N-1)]

MODE
----
Le mode se trouve généralement près de :
⌊(n+1)(K+1)/(N+2)⌋ ou ⌈(n+1)(K+1)/(N+2)⌉ - 1

ASYMÉTRIE (SKEWNESS)
-------------------
Skew = [(N-2K)(N-1)^(1/2)(N-2n)] / [√(nK(N-K)(N-n))(N-2)]

APPLICATIONS AUX JEUX DE CARTES
===============================

EXEMPLE 1 : CARTES DE COUR
--------------------------
Problème : Dans un jeu de 52 cartes, quelle est la probabilité d'obtenir exactement 2 cartes de cour (valet, dame, roi) en tirant 5 cartes sans remise ?

Données :
- N = 52 (total des cartes)
- K = 12 (cartes de cour : 4 valets + 4 dames + 4 rois)
- n = 5 (cartes tirées)
- k = 2 (cartes de cour désirées)

Calcul :
P(X = 2) = [C(12,2) × C(40,3)] / C(52,5)
P(X = 2) = [66 × 9880] / 2598960
P(X = 2) = 652080 / 2598960 ≈ 0.251

EXEMPLE 2 : AS DANS UNE MAIN DE POKER
-------------------------------------
Problème : Probabilité d'avoir exactement 1 as dans une main de 5 cartes.

Données :
- N = 52, K = 4 (as), n = 5, k = 1

Calcul :
P(X = 1) = [C(4,1) × C(48,4)] / C(52,5)
P(X = 1) = [4 × 194580] / 2598960 ≈ 0.299

EXEMPLE 3 : COULEUR SPÉCIFIQUE
------------------------------
Problème : Probabilité d'obtenir exactement 3 cartes de cœur en tirant 7 cartes.

Données :
- N = 52, K = 13 (cœurs), n = 7, k = 3

Calcul :
P(X = 3) = [C(13,3) × C(39,4)] / C(52,7)

COMPARAISON AVEC LA DISTRIBUTION BINOMIALE
==========================================

QUAND UTILISER CHAQUE DISTRIBUTION
----------------------------------

Distribution Hypergéométrique :
- Échantillonnage SANS remise
- Population finie
- Probabilité change après chaque tirage

Distribution Binomiale :
- Échantillonnage AVEC remise
- Population infinie ou très grande
- Probabilité constante pour chaque essai

APPROXIMATION BINOMIALE
-----------------------
Quand N est très grand par rapport à n (règle empirique : n/N < 0.05), 
la distribution hypergéométrique peut être approximée par une binomiale :

X ~ Binomial(n, p) où p = K/N

FACTEUR DE CORRECTION
--------------------
La variance hypergéométrique inclut le facteur (N-n)/(N-1) qui :
- Tend vers 1 quand N → ∞
- Réduit la variance par rapport à la binomiale
- Reflète l'effet de l'échantillonnage sans remise

FORMULES DÉRIVÉES ET EXTENSIONS
===============================

FONCTION DE RÉPARTITION (CDF)
-----------------------------
F(k) = P(X ≤ k) = Σ(i=0 to k) [C(K,i) × C(N-K,n-i)] / C(N,n)

PROBABILITÉ COMPLÉMENTAIRE
--------------------------
P(X ≥ k) = 1 - P(X ≤ k-1)

DISTRIBUTION HYPERGÉOMÉTRIQUE MULTIVARIÉE
-----------------------------------------
Pour plusieurs types de succès simultanément :
P(X₁=k₁, X₂=k₂, ..., Xₘ=kₘ) = [∏C(Kᵢ,kᵢ) × C(N-ΣKᵢ,n-Σkᵢ)] / C(N,n)

APPLICATIONS MODERNES
=====================

CONTRÔLE QUALITÉ
----------------
- Inspection d'échantillons de production
- Détection de défauts dans des lots
- Audit de conformité

ÉCOLOGIE ET BIOLOGIE
-------------------
- Capture-recapture d'animaux
- Échantillonnage de populations
- Études génétiques

SONDAGES ET ENQUÊTES
-------------------
- Échantillonnage stratifié
- Sélection d'échantillons représentatifs
- Correction pour population finie

JEUX ET PARIS
-------------
- Calculs de probabilités au poker
- Loteries et tirages
- Jeux de cartes stratégiques

MÉTHODES DE CALCUL PRATIQUES
============================

CALCUL MANUEL
-------------
1. Identifier N, K, n, k
2. Calculer C(K,k)
3. Calculer C(N-K,n-k)
4. Calculer C(N,n)
5. Appliquer la formule

OUTILS INFORMATIQUES
--------------------
- Excel : HYPGEOM.DIST(k, n, K, N)
- R : dhyper(k, K, N-K, n)
- Python : scipy.stats.hypergeom.pmf(k, N, K, n)
- Calculatrices scientifiques avec fonctions combinatoires

VÉRIFICATIONS ET VALIDATIONS
============================

TESTS DE COHÉRENCE
------------------
- Σ P(X = k) = 1 pour tous les k valides
- 0 ≤ P(X = k) ≤ 1 pour tout k
- E[X] = n × K/N
- Var(X) ≥ 0

CAS LIMITES
-----------
- Si K = 0 : P(X = 0) = 1, P(X > 0) = 0
- Si K = N : P(X = n) = 1
- Si n = 0 : P(X = 0) = 1
- Si n = N : P(X = K) = 1

ERREURS COURANTES À ÉVITER
==========================

1. Confondre avec la distribution binomiale
2. Mal identifier les paramètres N, K, n, k
3. Oublier les contraintes sur k
4. Erreurs de calcul dans les combinaisons
5. Mauvaise interprétation du contexte (avec/sans remise)
