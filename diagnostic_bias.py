#!/usr/bin/env python3
"""
Diagnostic approfondi du biais BANKER
"""

import sys
sys.path.append('.')

from baccarat_exhaustive_core import *
import numpy as np

def analyze_real_bias():
    """Analyse le vrai biais dans les données réelles"""
    
    print('=== DIAGNOSTIC APPROFONDI DU BIAIS BANKER ===')
    print()
    
    # 1. Analyser la distribution des outcomes
    print('1. DISTRIBUTION DES OUTCOMES')
    distribution = get_outcomes_distribution()
    
    # 2. Analyser un échantillon de probabilités réelles
    print('\n2. ANALYSE DES PROBABILITÉS RÉELLES')
    
    # Créer un moteur pour calculer des probabilités
    engine = BaccaratExhaustiveEngine(cores=2, ram_gb=4, top_k=10000)
    engine.file_manager.ensure_file_exists()
    engine.outcomes_manager.ensure_file_exists()
    
    # Séquence de test simple
    observed = [1, 0, 1, 0]  # 4 cartes observées
    sabot = SabotState(decks=8)
    sabot.subtract_observed(observed)
    
    # Calculer probabilités pour un échantillon
    print(f'Calcul probabilités pour séquence observée: {observed}')
    probabilities_with_indices = engine.probability_calculator.calculate_all_probabilities(
        sabot.current_counts, observed, True  # Avec exclusion TIE
    )
    
    # Analyser les TOP 1000 probabilités
    top_1000 = probabilities_with_indices[:1000]
    
    # Compter BANKER vs PLAYER dans le TOP 1000
    banker_count = 0
    player_count = 0
    banker_prob_sum = 0.0
    player_prob_sum = 0.0
    
    # Lire les outcomes directement du fichier
    with open(OUTCOMES_FILE, 'rb') as f:
        for idx, prob in top_1000:
            f.seek(idx)
            outcome_code = f.read(1)[0]
            if outcome_code == 1:  # BANKER
                banker_count += 1
                banker_prob_sum += prob
            elif outcome_code == 0:  # PLAYER
                player_count += 1
                player_prob_sum += prob
    
    print(f'\nTOP 1000 PROBABILITÉS:')
    print(f'  BANKER: {banker_count} sextuplets ({banker_count/1000*100:.1f}%)')
    print(f'  PLAYER: {player_count} sextuplets ({player_count/1000*100:.1f}%)')
    print(f'  Somme probabilités BANKER: {banker_prob_sum:.8f}')
    print(f'  Somme probabilités PLAYER: {player_prob_sum:.8f}')
    print(f'  Ratio masses: BANKER {banker_prob_sum/(banker_prob_sum+player_prob_sum)*100:.1f}% vs PLAYER {player_prob_sum/(banker_prob_sum+player_prob_sum)*100:.1f}%')
    
    # 3. Analyser les probabilités individuelles
    print(f'\n3. ANALYSE DES PROBABILITÉS INDIVIDUELLES')
    
    # Séparer BANKER et PLAYER
    banker_probs = []
    player_probs = []

    with open(OUTCOMES_FILE, 'rb') as f:
        for idx, prob in top_1000:
            f.seek(idx)
            outcome_code = f.read(1)[0]
            if outcome_code == 1:  # BANKER
                banker_probs.append(prob)
            elif outcome_code == 0:  # PLAYER
                player_probs.append(prob)
    
    if banker_probs and player_probs:
        print(f'  Probabilité moyenne BANKER: {np.mean(banker_probs):.8f}')
        print(f'  Probabilité moyenne PLAYER: {np.mean(player_probs):.8f}')
        print(f'  Probabilité max BANKER: {max(banker_probs):.8f}')
        print(f'  Probabilité max PLAYER: {max(player_probs):.8f}')
        
        # Le problème clé : les BANKER ont-ils des probabilités plus élevées ?
        avg_banker = np.mean(banker_probs)
        avg_player = np.mean(player_probs)
        
        if avg_banker > avg_player:
            bias_percent = (avg_banker - avg_player) / avg_player * 100
            print(f'  🚨 BIAIS DÉTECTÉ: BANKER a des probabilités {bias_percent:.1f}% plus élevées!')
        else:
            bias_percent = (avg_player - avg_banker) / avg_banker * 100
            print(f'  🚨 BIAIS DÉTECTÉ: PLAYER a des probabilités {bias_percent:.1f}% plus élevées!')
    
    # 4. Conclusion
    print(f'\n4. CONCLUSION')
    if banker_count > player_count:
        print(f'  Le biais vient de: Plus de sextuplets BANKER dans le TOP-K ({banker_count} vs {player_count})')
    
    if banker_prob_sum > player_prob_sum:
        print(f'  Le biais vient de: Masse probabiliste BANKER plus élevée ({banker_prob_sum:.6f} vs {player_prob_sum:.6f})')
    
    return {
        'top_banker_count': banker_count,
        'top_player_count': player_count,
        'banker_prob_sum': banker_prob_sum,
        'player_prob_sum': player_prob_sum
    }

if __name__ == "__main__":
    analyze_real_bias()
