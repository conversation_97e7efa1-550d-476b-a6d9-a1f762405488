#!/usr/bin/env python3
"""
Script d'analyse du problème de sur-correction de biais dans baccarat_exhaustive_core.py
"""

import sys
import os
import struct
import mmap
import numpy as np
from typing import Dict, List, Tuple

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.getcwd())

try:
    from baccarat_exhaustive_core import (
        get_outcomes_distribution, 
        get_base_rate_correction_factors,
        OUTCOMES_FILE,
        TOTAL_SEXTUPLETS
    )
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def analyze_current_bias():
    """Analyse le biais actuel dans la distribution des outcomes"""
    print("=== ANALYSE DU PROBLÈME DE BIAIS ===\n")
    
    # 1. Analyser la distribution brute
    print("1. Distribution brute des outcomes:")
    distribution = get_outcomes_distribution()
    
    non_tie_total = distribution['PLAYER'] + distribution['BANKER']
    banker_ratio = distribution['BANKER'] / non_tie_total
    player_ratio = distribution['PLAYER'] / non_tie_total
    
    print(f"   PLAYER: {distribution['PLAYER']:,} ({player_ratio*100:.3f}%)")
    print(f"   BANKER: {distribution['BANKER']:,} ({banker_ratio*100:.3f}%)")
    print(f"   TIE: {distribution['TIE']:,}")
    print(f"   Ratio BANKER/PLAYER: {banker_ratio/player_ratio:.6f}")
    
    # 2. Analyser les facteurs de correction actuels
    print("\n2. Facteurs de correction actuels:")
    correction_factors = get_base_rate_correction_factors()
    
    # 3. Simuler l'effet de la correction
    print("\n3. Simulation de l'effet de la correction:")
    
    # Probabilités simulées avant correction (basées sur la distribution réelle)
    simulated_banker_prob = 0.6  # Exemple: 60% des probabilités vont vers BANKER
    simulated_player_prob = 0.4  # Exemple: 40% des probabilités vont vers PLAYER
    
    # Après correction
    corrected_banker_prob = simulated_banker_prob * correction_factors['BANKER']
    corrected_player_prob = simulated_player_prob * correction_factors['PLAYER']
    
    print(f"   Avant correction: BANKER={simulated_banker_prob:.3f}, PLAYER={simulated_player_prob:.3f}")
    print(f"   Après correction: BANKER={corrected_banker_prob:.3f}, PLAYER={corrected_player_prob:.3f}")
    
    # Normalisation
    total_corrected = corrected_banker_prob + corrected_player_prob
    normalized_banker = corrected_banker_prob / total_corrected
    normalized_player = corrected_player_prob / total_corrected
    
    print(f"   Après normalisation: BANKER={normalized_banker:.3f}, PLAYER={normalized_player:.3f}")
    
    # 4. Identifier le problème
    print("\n4. Diagnostic du problème:")
    if correction_factors['PLAYER'] > 1.5:
        print(f"   ⚠️  SUR-CORRECTION DÉTECTÉE: Facteur PLAYER = {correction_factors['PLAYER']:.3f}")
        print("   → Les probabilités PLAYER sont multipliées par un facteur trop élevé")
        print("   → Cela explique pourquoi toutes les prédictions donnent PLAYER")
    
    if correction_factors['BANKER'] < 0.8:
        print(f"   ⚠️  SOUS-PONDÉRATION BANKER: Facteur BANKER = {correction_factors['BANKER']:.3f}")
        print("   → Les probabilités BANKER sont trop réduites")
    
    return {
        'distribution': distribution,
        'correction_factors': correction_factors,
        'banker_ratio': banker_ratio,
        'player_ratio': player_ratio
    }

def propose_solutions(analysis_result):
    """Propose des solutions pour corriger la sur-correction"""
    print("\n=== SOLUTIONS PROPOSÉES ===\n")
    
    correction_factors = analysis_result['correction_factors']
    banker_ratio = analysis_result['banker_ratio']
    player_ratio = analysis_result['player_ratio']
    
    print("1. SOLUTION SIMPLE - Facteurs de correction modérés:")
    # Utiliser une correction plus douce (racine carrée du facteur)
    moderate_banker_factor = np.sqrt(0.5 / banker_ratio)
    moderate_player_factor = np.sqrt(0.5 / player_ratio)
    
    print(f"   BANKER: {moderate_banker_factor:.6f} (au lieu de {correction_factors['BANKER']:.6f})")
    print(f"   PLAYER: {moderate_player_factor:.6f} (au lieu de {correction_factors['PLAYER']:.6f})")
    
    print("\n2. SOLUTION BAYÉSIENNE - Moyenne pondérée:")
    # Mélanger la correction avec les données originales
    alpha = 0.3  # Poids de la correction
    bayesian_banker_factor = alpha * (0.5 / banker_ratio) + (1 - alpha) * 1.0
    bayesian_player_factor = alpha * (0.5 / player_ratio) + (1 - alpha) * 1.0
    
    print(f"   BANKER: {bayesian_banker_factor:.6f} (correction à {alpha*100}%)")
    print(f"   PLAYER: {bayesian_player_factor:.6f} (correction à {alpha*100}%)")
    
    print("\n3. SOLUTION ISOTONIC REGRESSION - Calibration des probabilités:")
    print("   → Utiliser une régression isotonique pour calibrer les probabilités")
    print("   → Préserver l'ordre tout en corrigeant le biais")
    
    print("\n4. SOLUTION PLATT SCALING - Calibration sigmoïde:")
    print("   → Appliquer une transformation sigmoïde aux probabilités")
    print("   → Paramètres optimisés sur un ensemble de validation")
    
    return {
        'moderate': {'BANKER': moderate_banker_factor, 'PLAYER': moderate_player_factor},
        'bayesian': {'BANKER': bayesian_banker_factor, 'PLAYER': bayesian_player_factor}
    }

if __name__ == "__main__":
    try:
        analysis = analyze_current_bias()
        solutions = propose_solutions(analysis)
        
        print("\n=== RECOMMANDATION ===")
        print("La solution BAYÉSIENNE (option 2) est recommandée car elle:")
        print("- Corrige le biais de manière progressive")
        print("- Évite la sur-correction")
        print("- Préserve une partie de l'information originale")
        print("- Est mathématiquement robuste")
        
    except Exception as e:
        print(f"Erreur lors de l'analyse: {e}")
        import traceback
        traceback.print_exc()
