#!/usr/bin/env python3
"""
Test avec une séquence neutre pour identifier le biais
"""

import sys
sys.path.append('.')

from baccarat_exhaustive_core import *

def test_neutral_sequence():
    """Test avec une séquence équilibrée"""
    
    print('=== TEST AVEC SÉQUENCE NEUTRE ===')
    
    # Créer un moteur
    engine = BaccaratExhaustiveEngine(cores=2, ram_gb=4, top_k=100000)
    engine.file_manager.ensure_file_exists()
    engine.outcomes_manager.ensure_file_exists()
    
    # Séquence neutre : 4 de chaque valeur (équilibrée)
    neutral_sequence = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] * 3 + [0, 1, 2, 3, 4]  # 35 cartes
    
    print(f'Séquence neutre (35 cartes): {neutral_sequence}')
    print(f'Répartition: {[neutral_sequence.count(i) for i in range(10)]}')
    
    # Analyser avec cette séquence
    result = engine.analyze_with_intelligent_unified_fast(neutral_sequence)
    
    print(f'\nRÉSULTAT:')
    print(f'Recommandation: {result["recommendation"]}')
    print(f'PLAYER: {result.get("PLAYER", 0):.6f}')
    print(f'BANKER: {result.get("BANKER", 0):.6f}')
    print(f'Confiance: {result.get("confidence", 0)*100:.1f}%')
    
    return result

if __name__ == "__main__":
    test_neutral_sequence()
