#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exécute l'agrégation pondérée complète (1M) avec outcomes officiels
sur un sabot plein (8 decks) et affiche les masses P/B/T.
"""
from __future__ import annotations
from baccarat_exhaustive_core import FullMassAggregator, SabotState

def main():
    sabot = SabotState(decks=8)
    agg = FullMassAggregator(cores=4)
    res = agg.aggregate(sabot.get_counts_copy())
    print("Masses pondérées (sabot plein):")
    print(res)

if __name__ == "__main__":
    main()

