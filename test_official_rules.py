#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tests unitaires de la loi officielle corrigée sur 3 cas:
- P3_only: le Joueur tire, le Banquier ne tire pas -> 5 cartes
- B3_only: le Joueur ne tire pas, le Banquier tire (B3=v5) -> 5 cartes
- both: le Joueur tire puis le Banquier tire (B3=v6) -> 6 cartes
"""
from baccarat_exhaustive_core import apply_baccarat_law_fast

def test_p3_only():
    # P1=2, B1=1, P2=2, B2=2, P3=8, v6=9 (unused)
    # SP init = 4 (draw), SB init = 3; avec P3=8, Banquier NE TIRE PAS (règle SB=3 sauf P3=8)
    sext = (2, 1, 2, 2, 8, 9)
    outcome, used = apply_baccarat_law_fast(sext)
    assert used == 5, f"P3_only devrait consommer 5 cartes, got {used}"
    # SPf = (2+2+8)%10=2, SBf=3 => BANKER
    assert outcome == "BANKER", f"Attendu BANKER, got {outcome}"


def test_b3_only():
    # P1=4, B1=2, P2=3, B2=3, v5=B3=4, v6=9
    # SP init = 7 (stand), SB init = 5 (draw car joueur ne tire pas)
    sext = (4, 2, 3, 3, 4, 9)
    outcome, used = apply_baccarat_law_fast(sext)
    assert used == 5, f"B3_only devrait consommer 5 cartes (B3=v5), got {used}"
    # SBf = (2+3+4)%10=9 > SPf=7 => BANKER
    assert outcome == "BANKER", f"Attendu BANKER, got {outcome}"


def test_both():
    # P1=1, B1=2, P2=3, B2=2, P3=5 (J tire), B3=v6=7 (B tire car SB=4 et P3=5)
    sext = (1, 2, 3, 2, 5, 7)
    outcome, used = apply_baccarat_law_fast(sext)
    assert used == 6, f"both devrait consommer 6 cartes, got {used}"
    # SPf = (1+3+5)%10=9, SBf=(2+2+7)%10=1 => PLAYER
    assert outcome == "PLAYER", f"Attendu PLAYER, got {outcome}"


if __name__ == "__main__":
    # Exécution simple sans framework
    tests = [test_p3_only, test_b3_only, test_both]
    for t in tests:
        t()
    print("✓ Tous les tests unitaires officiels ont réussi.")

