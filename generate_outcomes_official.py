#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Génère outcomes_1M_official.bin en appliquant la loi baccarat CORRIGÉE
et compte le nombre de BANKER/PLAYER/TIE sur l'ensemble des 1,000,000 sextuplets.

Ce script lit sextuplets_1M.bin (6 bytes par sextuplet, valeurs 0..9)
applique apply_baccarat_law_fast (corrigée) et écrit outcomes_1M_official.bin
(1 byte par sextuplet: 0=PLAYER, 1=BANKER, 2=TIE). Il imprime ensuite les comptes.
"""
from __future__ import annotations
import mmap
import os
import struct
import time

from baccarat_exhaustive_core import (
    SEXTUPLETS_FILE,
    SEXTUPLET_SIZE,
    TOTAL_SEXTUPLETS,
    apply_baccarat_law_fast,
)

OUT_FILE_OFFICIAL = "outcomes_1M_official.bin"

OUTCOME_CODE = {"PLAYER": 0, "BANKER": 1, "TIE": 2}


def generate_outcomes_official(
    sextuplets_file: str = SEXTUPLETS_FILE,
    out_file: str = OUT_FILE_OFFICIAL,
) -> float:
    start = time.time()
    print(
        f"Création fichier outcomes officiel {out_file} pour {TOTAL_SEXTUPLETS:,} sextuplets..."
    )
    with open(sextuplets_file, "rb") as f_in, open(out_file, "wb") as f_out:
        with mmap.mmap(f_in.fileno(), 0, access=mmap.ACCESS_READ) as mm:
            for idx in range(TOTAL_SEXTUPLETS):
                off = idx * SEXTUPLET_SIZE
                mm.seek(off)
                data = mm.read(SEXTUPLET_SIZE)
                sextuplet = struct.unpack("BBBBBB", data)
                outcome, _used = apply_baccarat_law_fast(sextuplet)
                f_out.write(struct.pack("B", OUTCOME_CODE[outcome]))
                if (idx + 1) % 100_000 == 0:
                    print(f"  {idx + 1:,} outcomes calculés...")
    dt = time.time() - start
    size = os.path.getsize(out_file)
    print(f"✓ Fichier {out_file} créé en {dt:.2f}s ({size:,} bytes)")
    return dt


def count_outcomes(
    out_file: str = OUT_FILE_OFFICIAL,
) -> dict:
    counts = {"PLAYER": 0, "BANKER": 0, "TIE": 0}
    with open(out_file, "rb") as f:
        data = f.read()
    for b in data:
        if b == 0:
            counts["PLAYER"] += 1
        elif b == 1:
            counts["BANKER"] += 1
        else:
            counts["TIE"] += 1
    return counts


if __name__ == "__main__":
    # Générer outcomes officiels
    generate_outcomes_official()
    # Compter et afficher
    counts = count_outcomes()
    total = sum(counts.values())
    print("Résumé des issues sur 1,000,000 sextuplets (règles officielles):")
    print(f"  PLAYER: {counts['PLAYER']:,}")
    print(f"  BANKER: {counts['BANKER']:,}")
    print(f"  TIE:    {counts['TIE']:,}")
    print(f"  TOTAL:  {total:,}")

