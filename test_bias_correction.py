#!/usr/bin/env python3
"""
Test de la correction de biais BANKER/PLAYER
"""

import sys
sys.path.append('.')

from baccarat_exhaustive_core import MajorityVoter, SextupletWithOutcome

def test_bias_correction():
    """Test de la correction de biais avec données simulées"""
    
    print('=== TEST CORRECTION DE BIAIS ===')
    print('Données: 6 BANKER vs 4 PLAYER (masses égales de 0.1 chacune)')
    print()
    
    # Créer un voter
    voter = MajorityVoter()
    
    # Simuler 6 BANKER vs 4 PLAYER (biais vers BANKER)
    test_sextuplets = [
        SextupletWithOutcome((0,1,2,3,4,5), 0.1, 'BANKER', 6),
        SextupletWithOutcome((1,2,3,4,5,6), 0.1, 'BANKER', 6),
        SextupletWithOutcome((2,3,4,5,6,7), 0.1, 'BANKER', 6),
        SextupletWithOutcome((3,4,5,6,7,8), 0.1, 'BANKER', 6),
        SextupletWithOutcome((4,5,6,7,8,9), 0.1, 'BANKER', 6),
        SextupletWithOutcome((5,6,7,8,9,0), 0.1, 'BANKER', 6),
        SextupletWithOutcome((6,7,8,9,0,1), 0.1, 'PLAYER', 6),
        SextupletWithOutcome((7,8,9,0,1,2), 0.1, 'PLAYER', 6),
        SextupletWithOutcome((8,9,0,1,2,3), 0.1, 'PLAYER', 6),
        SextupletWithOutcome((9,0,1,2,3,4), 0.1, 'PLAYER', 6),
    ]
    
    # Test sans correction
    print('--- SANS CORRECTION ---')
    voter.bias_correction = False
    result_no_correction = voter.vote_weighted(test_sextuplets)
    
    print()
    
    # Test avec correction  
    print('--- AVEC CORRECTION ---')
    voter.bias_correction = True
    result_with_correction = voter.vote_weighted(test_sextuplets)
    
    print()
    print('=== RÉSULTATS COMPARÉS ===')
    print(f'Sans correction: {result_no_correction["recommendation"]}')
    print(f'  BANKER: {result_no_correction["BANKER"]:.6f}')
    print(f'  PLAYER: {result_no_correction["PLAYER"]:.6f}')
    print()
    print(f'Avec correction: {result_with_correction["recommendation"]}')
    print(f'  BANKER: {result_with_correction["BANKER"]:.6f}')
    print(f'  PLAYER: {result_with_correction["PLAYER"]:.6f}')
    
    # Analyse
    print()
    print('=== ANALYSE ===')
    if result_no_correction["recommendation"] != result_with_correction["recommendation"]:
        print('✅ CORRECTION EFFICACE: La recommandation a changé!')
    else:
        print('⚠️  Même recommandation, mais masses ajustées')
    
    return result_no_correction, result_with_correction

if __name__ == "__main__":
    test_bias_correction()
