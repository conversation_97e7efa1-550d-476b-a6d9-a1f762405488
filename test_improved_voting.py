#!/usr/bin/env python3
"""
Test du système de vote amélioré avec différentes méthodes scientifiques
"""

import sys
import os
import time
from typing import List, Dict

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.getcwd())

try:
    from baccarat_exhaustive_core import BaccaratExhaustiveEngine
    from improved_voting_system import EnsembleVotingSystem, SextupletWithOutcome
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def test_voting_methods():
    """Test les différentes méthodes de vote sur un exemple concret"""
    print("=== TEST DES MÉTHODES DE VOTE AMÉLIORÉES ===\n")
    
    # Configuration du sabot de test
    test_shoe = [
        'A♠', '2♥', '3♦', '4♣', '5♠', '6♥',  # Cartes restantes dans le sabot
        '7♦', '8♣', '9♠', '10♥', 'J♦', 'Q♣'
    ]
    
    print(f"Sabot de test: {test_shoe}")
    print(f"Nombre de cartes: {len(test_shoe)}\n")
    
    # Méthodes de vote à tester
    voting_methods = ['standard', 'bma', 'isotonic', 'platt']
    results = {}
    
    for method in voting_methods:
        print(f"--- Test méthode: {method.upper()} ---")
        
        try:
            # Créer le moteur avec la méthode spécifiée
            engine = BaccaratExhaustiveEngine(
                cores=4,  # Réduire pour le test
                ram_gb=16,
                top_k=100000,  # Réduire pour le test
                voting_method=method
            )
            
            start_time = time.time()
            
            # Analyser le sabot
            result = engine.analyze_shoe(test_shoe)
            
            analysis_time = time.time() - start_time
            
            # Stocker les résultats
            results[method] = {
                'recommendation': result.get('recommendation', '—'),
                'confidence': result.get('confidence', 0.0),
                'player_mass': result.get('PLAYER', 0.0),
                'banker_mass': result.get('BANKER', 0.0),
                'time': analysis_time,
                'total_sextuplets': result.get('total', 0)
            }
            
            print(f"  Recommandation: {result.get('recommendation', '—')}")
            print(f"  Confiance: {result.get('confidence', 0.0)*100:.1f}%")
            print(f"  Masse PLAYER: {result.get('PLAYER', 0.0):.6f}")
            print(f"  Masse BANKER: {result.get('BANKER', 0.0):.6f}")
            print(f"  Temps d'analyse: {analysis_time:.2f}s")
            print(f"  Sextuplets analysés: {result.get('total', 0):,}")
            print()
            
        except Exception as e:
            print(f"  ❌ Erreur avec méthode {method}: {e}")
            results[method] = {
                'recommendation': 'ERREUR',
                'confidence': 0.0,
                'player_mass': 0.0,
                'banker_mass': 0.0,
                'time': 0.0,
                'total_sextuplets': 0,
                'error': str(e)
            }
            print()
    
    # Analyse comparative
    print("=== ANALYSE COMPARATIVE ===\n")
    
    # Vérifier la cohérence des recommandations
    recommendations = [r['recommendation'] for r in results.values() if r['recommendation'] != 'ERREUR']
    unique_recommendations = set(recommendations)
    
    if len(unique_recommendations) == 1:
        print("✅ Toutes les méthodes donnent la même recommandation")
    else:
        print("⚠️  Les méthodes donnent des recommandations différentes:")
        for method, result in results.items():
            if result['recommendation'] != 'ERREUR':
                print(f"   {method.upper()}: {result['recommendation']} ({result['confidence']*100:.1f}%)")
    
    print()
    
    # Analyser les performances
    print("Performance par méthode:")
    for method, result in results.items():
        if result['recommendation'] != 'ERREUR':
            print(f"  {method.upper()}: {result['time']:.2f}s")
    
    print()
    
    # Analyser la stabilité des masses
    print("Stabilité des masses de probabilité:")
    player_masses = [r['player_mass'] for r in results.values() if r['recommendation'] != 'ERREUR']
    banker_masses = [r['banker_mass'] for r in results.values() if r['recommendation'] != 'ERREUR']
    
    if player_masses and banker_masses:
        player_std = (max(player_masses) - min(player_masses)) / max(player_masses) if max(player_masses) > 0 else 0
        banker_std = (max(banker_masses) - min(banker_masses)) / max(banker_masses) if max(banker_masses) > 0 else 0
        
        print(f"  Variation PLAYER: {player_std*100:.2f}%")
        print(f"  Variation BANKER: {banker_std*100:.2f}%")
        
        if player_std < 0.05 and banker_std < 0.05:
            print("  ✅ Masses stables entre les méthodes")
        else:
            print("  ⚠️  Variations significatives entre les méthodes")
    
    return results

def test_direct_ensemble_voting():
    """Test direct du système de vote d'ensemble"""
    print("\n=== TEST DIRECT DU SYSTÈME D'ENSEMBLE ===\n")
    
    # Créer des données de test
    test_sextuplets = [
        SextupletWithOutcome((1, 2, 3, 4, 5, 6), 0.001, 'PLAYER', 6),
        SextupletWithOutcome((2, 3, 4, 5, 6, 7), 0.002, 'BANKER', 6),
        SextupletWithOutcome((3, 4, 5, 6, 7, 8), 0.0015, 'PLAYER', 6),
        SextupletWithOutcome((4, 5, 6, 7, 8, 9), 0.0025, 'BANKER', 6),
        SextupletWithOutcome((5, 6, 7, 8, 9, 10), 0.003, 'PLAYER', 6),
    ]
    
    methods = ['bma', 'isotonic', 'platt']
    
    for method in methods:
        print(f"Test {method.upper()}:")
        
        try:
            ensemble = EnsembleVotingSystem(method=method)
            result = ensemble.vote(test_sextuplets)
            
            print(f"  Recommandation: {result['recommendation']}")
            print(f"  Confiance: {result['confidence']*100:.1f}%")
            print(f"  PLAYER: {result['PLAYER']:.6f}")
            print(f"  BANKER: {result['BANKER']:.6f}")
            print()
            
        except Exception as e:
            print(f"  ❌ Erreur: {e}")
            print()

def main():
    """Fonction principale"""
    print("SYSTÈME DE VOTE AMÉLIORÉ - TESTS COMPLETS")
    print("=" * 60)
    
    # Test 1: Méthodes de vote intégrées
    try:
        results = test_voting_methods()
    except Exception as e:
        print(f"Erreur lors du test des méthodes intégrées: {e}")
        results = {}
    
    # Test 2: Système d'ensemble direct
    try:
        test_direct_ensemble_voting()
    except Exception as e:
        print(f"Erreur lors du test direct: {e}")
    
    # Recommandations finales
    print("=== RECOMMANDATIONS ===")
    
    if results:
        # Trouver la méthode la plus rapide et stable
        valid_results = {k: v for k, v in results.items() if v['recommendation'] != 'ERREUR'}
        
        if valid_results:
            fastest_method = min(valid_results.keys(), key=lambda k: valid_results[k]['time'])
            most_confident = max(valid_results.keys(), key=lambda k: valid_results[k]['confidence'])
            
            print(f"✅ Méthode la plus rapide: {fastest_method.upper()}")
            print(f"✅ Méthode la plus confiante: {most_confident.upper()}")
            
            if 'bma' in valid_results:
                print("✅ Recommandation: Utiliser BMA (Bayesian Model Averaging)")
                print("   → Équilibre optimal entre précision et robustesse")
                print("   → Méthode scientifiquement validée")
                print("   → Gestion automatique de l'incertitude")
            else:
                print(f"✅ Recommandation alternative: {fastest_method.upper()}")
        else:
            print("❌ Aucune méthode n'a fonctionné correctement")
    else:
        print("❌ Impossible de tester les méthodes intégrées")
    
    print("\n📚 Références scientifiques:")
    print("   - Hoeting et al. (1999): Bayesian Model Averaging")
    print("   - Zadrozny & Elkan (2002): Isotonic Regression")
    print("   - Platt (1999): Probabilistic Outputs for SVMs")

if __name__ == "__main__":
    main()
