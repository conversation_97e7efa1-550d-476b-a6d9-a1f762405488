#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Baccarat Exhaustive GUI - INTERFACE PROFESSIONNELLE
===================================================

Interface Tkinter optimisée pour le nouveau moteur d'analyse :
- Saisie ordre visuel des cartes (4-6 chiffres 0-9)
- Pipeline complet en 6 étapes avec métriques temps réel
- Utilisation optimale des 8 cœurs + 32GB RAM
- Analyse exhaustive de 1M sextuplets → TOP 500k → Vote majoritaire
- Affichage performance et recommandations avec confiance

Architecture :
- Moteur principal : BaccaratExhaustiveEngine
- Pipeline : Génération → Probabilités → Sélection → Loi → Vote
- Optimisations : Batching intelligent, parallélisation, gestion mémoire
"""
import tkinter as tk
from tkinter import ttk
from typing import List
import threading
import time

from baccarat_exhaustive_core import (
    BaccaratExhaustiveEngine,
    SabotState,
    invert_visual_to_draw,
    # Compatibilité ancienne API (deprecated)
    initial_counts_for_decks,
    subtract_observed,
    enumerate_sextuplets_exhaustive,
    majority_vote_topk,
)

class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Baccarat Exhaustif PROFESSIONNEL - Pipeline 6 Étapes (1M→500k)")
        self.geometry("1000x800")
        self.resizable(True, True)

        # Moteur principal optimisé
        self.engine = BaccaratExhaustiveEngine(cores=8, ram_gb=32, top_k=500000)

        # État de l'application
        self.sabot_state = SabotState(decks=8)
        self.hands = []
        self.is_analyzing = False
        # Brûlage initial (facultatif) - 1 carte révélée (10/J/Q/K = 0)
        self.initial_burn_applied = False
        self.initial_burn_value = None
        self.var_burn = tk.StringVar(value="")


        self._build()
        self._reset()

    def _build(self):
        pad = 10
        root = ttk.Frame(self, padding=12)
        root.pack(fill=tk.BOTH, expand=True)
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)

        # === SECTION SAISIE ===
        frame_input = ttk.LabelFrame(root, text="Saisie cartes visuelles")
        frame_input.pack(fill=tk.X, pady=(0, pad))

        ttk.Label(frame_input, text="Ordre visuel (4-6 chiffres 0-9): [P3 si], P1, P2, B1, B2, [B3 si]").pack(anchor=tk.W, padx=8, pady=4)
        ttk.Label(frame_input, text="Appuyez sur ENTRÉE pour ajouter la main ET lancer l'analyse automatiquement",
                 font=("Segoe UI", 9), foreground="blue").pack(anchor=tk.W, padx=8, pady=(0,4))

        # NOUVELLE SECTION : Info sur les modes améliorés
        info_frame = ttk.Frame(frame_input)
        info_frame.pack(fill=tk.X, padx=8, pady=2)
        self.var_mode_info = tk.StringVar(value="Mode standard avec cache adaptatif et optimisations")
        ttk.Label(info_frame, textvariable=self.var_mode_info, font=("Segoe UI", 8), foreground="darkgreen").pack(anchor=tk.W)

        input_row = ttk.Frame(frame_input)
        input_row.pack(fill=tk.X, padx=8, pady=4)

        self.var_input = tk.StringVar()
        self.entry = ttk.Entry(input_row, textvariable=self.var_input, width=20, font=("Consolas", 12))
        self.entry.pack(side=tk.LEFT)
        self.entry.focus_set()
        # Champ brûlage initial
        burn_row = ttk.Frame(frame_input)
        burn_row.pack(fill=tk.X, padx=8, pady=(0,4))
        ttk.Label(burn_row, text="Carte brûlée initiale (0-9, 10/J/Q/K = 0):").pack(side=tk.LEFT)
        ent_burn = ttk.Entry(burn_row, textvariable=self.var_burn, width=6)
        ent_burn.pack(side=tk.LEFT, padx=(4,0))
        ttk.Label(burn_row, text="(appliquée une fois, avant la première main)", foreground="gray").pack(side=tk.LEFT, padx=(6,0))

        self.entry.bind('<Return>', lambda e: self.on_add_and_analyze())

        ttk.Button(input_row, text="Ajouter main", command=self.on_add_and_analyze).pack(side=tk.LEFT, padx=(8,0))
        ttk.Button(input_row, text="Annuler dernière", command=self.on_undo).pack(side=tk.LEFT, padx=(4,0))
        ttk.Button(input_row, text="Réinitialiser", command=self.on_reset).pack(side=tk.LEFT, padx=(4,0))

        # === SECTION ÉTAT ACTUEL ===
        frame_current = ttk.LabelFrame(root, text="État actuel")
        frame_current.pack(fill=tk.X, pady=(pad, 0))

        # Main courante
        current_row1 = ttk.Frame(frame_current)
        current_row1.pack(fill=tk.X, padx=8, pady=4)
        ttk.Label(current_row1, text="Ordre réel:", width=15).pack(side=tk.LEFT)
        self.var_draw = tk.StringVar(value="—")
        ttk.Label(current_row1, textvariable=self.var_draw, font=("Consolas", 12), foreground="blue").pack(side=tk.LEFT)

        # Séquence complète
        current_row2 = ttk.Frame(frame_current)
        current_row2.pack(fill=tk.X, padx=8, pady=4)
        ttk.Label(current_row2, text="Séquence:", width=15).pack(side=tk.LEFT)
        self.var_sequence = tk.StringVar(value="")
        ttk.Label(current_row2, textvariable=self.var_sequence, font=("Consolas", 10)).pack(side=tk.LEFT)

        # Statistiques sabot
        current_row3 = ttk.Frame(frame_current)
        current_row3.pack(fill=tk.X, padx=8, pady=4)
        ttk.Label(current_row3, text="Cartes restantes:", width=15).pack(side=tk.LEFT)
        self.var_remaining = tk.StringVar(value="416")
        ttk.Label(current_row3, textvariable=self.var_remaining, font=("Consolas", 12), foreground="green").pack(side=tk.LEFT)
        ttk.Label(current_row3, text="Mains jouées:", width=15).pack(side=tk.LEFT, padx=(20,0))
        self.var_hands_count = tk.StringVar(value="0")
        ttk.Label(current_row3, textvariable=self.var_hands_count, font=("Consolas", 12), foreground="green").pack(side=tk.LEFT)

        # === SECTION ANALYSE PIPELINE ===
        frame_analysis = ttk.LabelFrame(root, text="Analyse Pipeline (Modes certifiés)")
        frame_analysis.pack(fill=tk.X, pady=(pad, 0))

        # Bouton analyse + statut
        analysis_row1 = ttk.Frame(frame_analysis)
        analysis_row1.pack(fill=tk.X, padx=8, pady=4)

        self.btn_analyze = ttk.Button(analysis_row1, text="🚀 ANALYSER PROCHAINE MAIN", command=self.on_analyze_async)
        self.btn_analyze.pack(side=tk.LEFT)

        # Indicateur d'analyse automatique
        ttk.Label(analysis_row1, text="(Analyse automatique après ENTRÉE)", font=("Segoe UI", 9), foreground="gray").pack(side=tk.LEFT, padx=(10,0))

        # Sélecteur de mode AMÉLIORÉ
        ttk.Label(analysis_row1, text="Mode:").pack(side=tk.LEFT, padx=(20,4))
        self.var_mode = tk.StringVar(value='intelligent_unified')  # Mode unifié par défaut
        # NOUVEAUX MODES AJOUTÉS : intelligent_unified (rapide par défaut), puis version complète
        mode_values = [
            'intelligent_unified',          # MODE UNIFIÉ RAPIDE (<5s)
            'intelligent_unified_complete', # MODE UNIFIÉ COMPLET (>60s)
            'full_support_exact', 'auto', 'topk_fixed', 'topk_adaptive', 'full_certified',
            'max_seq_no_tie', 'max_seq_no_tie_conditional',
            'bayesian_adaptive', 'mcts_exploration', 'genetic_optimized', 'multivariate_analysis'
        ]
        self.cmb_mode = ttk.Combobox(analysis_row1, textvariable=self.var_mode, state='readonly', values=mode_values, width=22)
        self.cmb_mode.pack(side=tk.LEFT)
        self.cmb_mode.bind('<<ComboboxSelected>>', lambda e: self._update_mode_ui())

        # Paramètre epsilon (pour topk_adaptive)
        ttk.Label(analysis_row1, text="ε:").pack(side=tk.LEFT, padx=(10,4))
        self.var_epsilon = tk.StringVar(value='1e-3')
        self.ent_eps = ttk.Entry(analysis_row1, textvariable=self.var_epsilon, width=8)
        self.ent_eps.pack(side=tk.LEFT)

        # Top‑K primaire fixe (coupure initiale)
        ttk.Label(analysis_row1, text="Top‑K primaire:").pack(side=tk.LEFT, padx=(10,4))
        self.var_topk_primary = tk.StringVar(value='500000')
        self.ent_topk_primary = ttk.Entry(analysis_row1, textvariable=self.var_topk_primary, width=10)
        self.ent_topk_primary.pack(side=tk.LEFT)

        # Top‑K interne (dans l'échantillon primaire) si mode topk_fixed interne
        ttk.Label(analysis_row1, text="Top‑K interne:").pack(side=tk.LEFT, padx=(10,4))
        self.var_topk_inner = tk.StringVar(value='500000')
        self.ent_topk_inner = ttk.Entry(analysis_row1, textvariable=self.var_topk_inner, width=10)
        self.ent_topk_inner.pack(side=tk.LEFT)

        # Robustesse (P_min)
        self.var_robust = tk.BooleanVar(value=False)
        self.chk_robust = ttk.Checkbutton(analysis_row1, text="Robuste P_min", variable=self.var_robust)
        self.chk_robust.pack(side=tk.LEFT, padx=(20,4))
        ttk.Label(analysis_row1, text="Budget:").pack(side=tk.LEFT, padx=(4,0))
        self.var_robust_budget = tk.StringVar(value='0')
        self.ent_robust_budget = ttk.Entry(analysis_row1, textvariable=self.var_robust_budget, width=6)
        self.ent_robust_budget.pack(side=tk.LEFT)

        # === SECTION PARAMÈTRES AMÉLIORATIONS ===
        enhancements_row = ttk.Frame(frame_analysis)
        enhancements_row.pack(fill=tk.X, padx=8, pady=4)

        # Cache adaptatif
        ttk.Label(enhancements_row, text="Cache:").pack(side=tk.LEFT)
        self.var_cache_size = tk.StringVar(value='100000')
        ttk.Entry(enhancements_row, textvariable=self.var_cache_size, width=8).pack(side=tk.LEFT, padx=(4,10))

        # MCTS iterations
        ttk.Label(enhancements_row, text="MCTS iter:").pack(side=tk.LEFT)
        self.var_mcts_iterations = tk.StringVar(value='10000')
        ttk.Entry(enhancements_row, textvariable=self.var_mcts_iterations, width=8).pack(side=tk.LEFT, padx=(4,10))

        # Algorithmes génétiques
        ttk.Label(enhancements_row, text="Génétique pop:").pack(side=tk.LEFT)
        self.var_genetic_population = tk.StringVar(value='30')
        ttk.Entry(enhancements_row, textvariable=self.var_genetic_population, width=6).pack(side=tk.LEFT, padx=(4,10))

        # Optimisation automatique
        self.var_auto_optimize = tk.BooleanVar(value=True)
        ttk.Checkbutton(enhancements_row, text="Auto-optimisation", variable=self.var_auto_optimize).pack(side=tk.LEFT, padx=(10,0))

        # === INFO : EXCLUSION TIE AUTOMATIQUE ===
        tie_info_row = ttk.Frame(frame_analysis)
        tie_info_row.pack(fill=tk.X, padx=8, pady=4)

        # Information sur l'exclusion TIE automatique (pas de case à cocher)
        ttk.Label(tie_info_row, text="ℹ️ Exclusion TIE automatique pour TOUS les modes",
                 font=("Segoe UI", 9), foreground="darkgreen").pack(side=tk.LEFT)
        ttk.Label(tie_info_row, text="(tri par probabilité décroissante)",
                 font=("Segoe UI", 8), foreground="gray").pack(side=tk.LEFT, padx=(10,0))

        # === CORRECTION DE BIAIS BANKER/PLAYER ===
        bias_correction_row = ttk.Frame(frame_analysis)
        bias_correction_row.pack(fill=tk.X, padx=8, pady=4)

        # Option de correction du biais (basée sur données réelles)
        self.var_bias_correction = tk.BooleanVar(value=True)
        ttk.Checkbutton(bias_correction_row, text="Corriger biais BANKER/PLAYER",
                       variable=self.var_bias_correction).pack(side=tk.LEFT)
        ttk.Label(bias_correction_row, text="(facteurs mathématiques: BANKER -1.38%, PLAYER +1.42%)",
                 font=("Segoe UI", 8), foreground="blue").pack(side=tk.LEFT, padx=(10,0))

        self.var_status = tk.StringVar(value="Prêt")
        ttk.Label(analysis_row1, text="Statut:").pack(side=tk.LEFT, padx=(20,4))
        self.lbl_status = ttk.Label(analysis_row1, textvariable=self.var_status, font=("Consolas", 10))
        self.lbl_status.pack(side=tk.LEFT)

        # Barre de progression
        analysis_row2 = ttk.Frame(frame_analysis)
        analysis_row2.pack(fill=tk.X, padx=8, pady=4)

        ttk.Label(analysis_row2, text="Progression:").pack(side=tk.LEFT)
        self.progress_bar = ttk.Progressbar(analysis_row2, mode='indeterminate', length=300)
        self.progress_bar.pack(side=tk.LEFT, padx=(8,0))

        # === SECTION RÉSULTATS ===
        frame_results = ttk.LabelFrame(root, text="Résultats d'analyse")
        frame_results.pack(fill=tk.X, pady=(pad, 0))
        # Bloc Confiance exacte / Incertitude / Robuste
        robust_frame = ttk.Frame(frame_results)
        robust_frame.pack(fill=tk.X, padx=8, pady=(0,4))
        ttk.Label(robust_frame, text="Incertitude:").pack(side=tk.LEFT)
        self.var_uncertainty = tk.StringVar(value="—")
        ttk.Label(robust_frame, textvariable=self.var_uncertainty, font=("Consolas", 12)).pack(side=tk.LEFT, padx=(4,20))
        ttk.Label(robust_frame, text="Robuste L1=1/2:").pack(side=tk.LEFT)
        self.var_robust_info = tk.StringVar(value="—")
        ttk.Label(robust_frame, textvariable=self.var_robust_info, font=("Consolas", 10)).pack(side=tk.LEFT, padx=(4,0))


        # Recommandation principale
        reco_frame = ttk.Frame(frame_results)
        reco_frame.pack(fill=tk.X, padx=8, pady=8)

        ttk.Label(reco_frame, text="RECOMMANDATION:", font=("Segoe UI", 12, 'bold')).pack(anchor=tk.CENTER)
        self.lbl_reco = tk.Label(reco_frame, text="—", font=("Segoe UI", 36, 'bold'), fg="#666666")
        self.lbl_reco.pack(anchor=tk.CENTER, pady=0)
        # Annotation alignée sous la recommandation (noir, plus petit)
        self.var_reco_note = tk.StringVar(value="—")
        self.lbl_reco_note = tk.Label(reco_frame, textvariable=self.var_reco_note, font=("Segoe UI", 10), fg="#000000")
        self.lbl_reco_note.pack(anchor=tk.CENTER, pady=(2,6))

        # Confiance et votes
        results_row = ttk.Frame(frame_results)
        results_row.pack(fill=tk.X, padx=8, pady=4)

        ttk.Label(results_row, text="Confiance:").pack(side=tk.LEFT)
        self.var_confidence = tk.StringVar(value="—")
        ttk.Label(results_row, textvariable=self.var_confidence, font=("Consolas", 12), foreground="blue").pack(side=tk.LEFT, padx=(4,20))

        ttk.Label(results_row, text="Votes:").pack(side=tk.LEFT)
        self.var_votes = tk.StringVar(value="P:— B:— T:—")
        ttk.Label(results_row, textvariable=self.var_votes, font=("Consolas", 12)).pack(side=tk.LEFT, padx=(4,0))

        # === SECTION MÉTRIQUES PERFORMANCE ===
        frame_metrics = ttk.LabelFrame(root, text="Métriques de performance")
        frame_metrics.pack(fill=tk.X, pady=(pad, 0))

        metrics_row1 = ttk.Frame(frame_metrics)
        metrics_row1.pack(fill=tk.X, padx=8, pady=4)

        ttk.Label(metrics_row1, text="Temps total:").pack(side=tk.LEFT)
        self.var_total_time = tk.StringVar(value="—")
        ttk.Label(metrics_row1, textvariable=self.var_total_time, font=("Consolas", 10)).pack(side=tk.LEFT, padx=(4,20))

        ttk.Label(metrics_row1, text="Mémoire pic:").pack(side=tk.LEFT)
        self.var_memory = tk.StringVar(value="—")
        ttk.Label(metrics_row1, textvariable=self.var_memory, font=("Consolas", 10)).pack(side=tk.LEFT, padx=(4,20))

        ttk.Label(metrics_row1, text="Sextuplets analysés:").pack(side=tk.LEFT)
        self.var_analyzed = tk.StringVar(value="—")
        ttk.Label(metrics_row1, textvariable=self.var_analyzed, font=("Consolas", 10)).pack(side=tk.LEFT, padx=(4,20))

        # NOUVELLES MÉTRIQUES AMÉLIORÉES
        ttk.Label(metrics_row1, text="Cache hit:").pack(side=tk.LEFT)
        self.var_cache_hit_rate = tk.StringVar(value="—")
        ttk.Label(metrics_row1, textvariable=self.var_cache_hit_rate, font=("Consolas", 10)).pack(side=tk.LEFT, padx=(4,0))

        # Détail des étapes

        # === SECTION CALIBRATION & COUVERTURE ===
        cal_frame = ttk.LabelFrame(root, text="Calibration & Couverture")
        cal_frame.pack(fill=tk.X, pady=(pad, 0))

        cal_row = ttk.Frame(cal_frame)
        cal_row.pack(fill=tk.X, padx=8, pady=4)
        ttk.Label(cal_row, text="Calibration (Bernstein):").pack(side=tk.LEFT)
        self.var_calibration = tk.StringVar(value="—")
        ttk.Label(cal_row, textvariable=self.var_calibration, font=("Consolas", 10)).pack(side=tk.LEFT, padx=(4,20))

        cov_row = ttk.Frame(cal_frame)
        cov_row.pack(fill=tk.X, padx=8, pady=4)
        ttk.Label(cov_row, text="Couverture vs K:").pack(side=tk.LEFT)
        self.var_coverage = tk.StringVar(value="—")
        ttk.Label(cov_row, textvariable=self.var_coverage, font=("Consolas", 10)).pack(side=tk.LEFT, padx=(4,0))

        metrics_row2 = ttk.Frame(frame_metrics)
        metrics_row2.pack(fill=tk.X, padx=8, pady=4)

        self.var_step_times = tk.StringVar(value="Gen:— Prob:— Sel:— Loi:— Vote:—")
        ttk.Label(metrics_row2, text="Étapes:").pack(side=tk.LEFT)
        ttk.Label(metrics_row2, textvariable=self.var_step_times, font=("Consolas", 9)).pack(side=tk.LEFT, padx=(4,0))

        # === SECTION HISTORIQUE ===
        frame_history = ttk.LabelFrame(root, text="Historique des mains")
        frame_history.pack(fill=tk.BOTH, expand=True, pady=(pad, 0))

        self.tree = ttk.Treeview(frame_history, columns=('visual', 'real', 'recommendation'), show='tree headings', height=6)
        self.tree.heading('#0', text='#')
        self.tree.heading('visual', text='Visuel')
        self.tree.heading('real', text='Ordre réel')
        self.tree.heading('recommendation', text='Recommandation')
        self.tree.column('#0', width=40)
        self.tree.column('visual', width=80)
        self.tree.column('real', width=120)
        self.tree.column('recommendation', width=120)

        scrollbar = ttk.Scrollbar(frame_history, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8,0), pady=8)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=8, padx=(0,8))
        # Met à jour l'ergonomie selon le mode
        self._update_mode_ui()


        # Messages d'erreur
        self.var_msg = tk.StringVar(value="")
        ttk.Label(root, textvariable=self.var_msg, foreground='red').pack(anchor=tk.W, pady=(pad, 0))

    def _reset(self):
        """Réinitialise l'état de l'application"""
        self.sabot_state.reset()
        self.hands.clear()
        self.is_analyzing = False
        self.initial_burn_applied = False
        self.initial_burn_value = None
        self.var_burn.set("")

        # Interface
        self.var_draw.set("—")
        self.var_sequence.set("")
        self.var_remaining.set("416")
        self.var_hands_count.set("0")
        self.var_status.set("Prêt")
        self.var_confidence.set("—")
        self.var_votes.set("P:— B:— T:—")
        self.var_total_time.set("—")
        self.var_memory.set("—")
        self.var_analyzed.set("—")
        self.var_step_times.set("Gen:— Prob:— Sel:— Loi:— Vote:—")
        self.var_msg.set("")

        # RÉINITIALISATION DES NOUVELLES MÉTRIQUES
        if hasattr(self, 'var_cache_hit_rate'):
            self.var_cache_hit_rate.set("—")

        self.lbl_reco.config(text="—", fg="#666666")
        self.progress_bar.stop()
        self.btn_analyze.config(state='normal')

        # Historique
        for item in self.tree.get_children():
            self.tree.delete(item)

    def _update_display(self):
        """Met à jour l'affichage après changement d'état"""
        # Séquence complète
        sequence = ''.join(str(v) for v in self.sabot_state.observed_sequence)
        self.var_sequence.set(sequence if sequence else "—")

        # Cartes restantes
        self.var_remaining.set(str(self.sabot_state.total_remaining))

        # Nombre de mains
        self.var_hands_count.set(str(len(self.hands)))

    def on_add(self):
        """Ajoute une nouvelle main (sans analyse automatique)"""
        if self.is_analyzing:
            self.var_msg.set("Analyse en cours, veuillez attendre...")
            return

        s = (self.var_input.get() or '').strip()
        if not s:
            return

        try:
            # Inversion visuelle
            res = invert_visual_to_draw(s)

            # Mise à jour état
            observed = [int(ch) for ch in res['draw_seq']]
            self.sabot_state.subtract_observed(observed)

            # Enregistrement main
            hand_data = {**res, 'visual': s, 'recommendation': '—'}
            self.hands.append(hand_data)

            # Mise à jour affichage
            self.var_draw.set(res['draw_seq'])
            self.var_input.set("")
            self._update_display()

            # Ajouter à l'historique
            hand_num = len(self.hands)
            self.tree.insert('', 'end', text=str(hand_num), values=(s, res['draw_seq'], '—'))

            self.var_msg.set("")
            return True  # Succès

        except Exception as e:
            self.var_msg.set(f"Erreur: {str(e)}")
            return False  # Échec

    def on_add_and_analyze(self):
        """Ajoute une nouvelle main ET lance l'analyse automatiquement"""
        if self.is_analyzing:
            self.var_msg.set("Analyse en cours, veuillez attendre...")
            return

        # Appliquer le brûlage initial une seule fois, si fourni et avant la première main
        try:
            if not self.initial_burn_applied and len(self.hands) == 0:
                s = self.var_burn.get().strip()
                if s != "":
                    if not s.isdigit() or not (0 <= int(s) <= 9):
                        self.var_msg.set("Brûlage initial invalide (entrez 0-9)")
                        return
                    burn_val = int(s)
                    # Soustraire la carte révélée (10/J/Q/K ont été saisis 0)
                    self.sabot_state.subtract_observed([burn_val])
                    self.initial_burn_applied = True
                    # Clear le champ pour éviter réapplications
                    self.var_burn.set("")
        except Exception as e:
            self.var_msg.set(f"Erreur brûlage initial: {str(e)}")
            return

        # D'abord ajouter la main
        success = self.on_add()

        if success and len(self.hands) > 0:
            # Puis lancer l'analyse automatiquement
            self.var_msg.set("Main ajoutée, analyse en cours...")
            # Petit délai pour que l'interface se mette à jour
            self.after(100, self.on_analyze_async)

            # Remettre le focus sur le champ de saisie pour la prochaine main
            self.after(200, lambda: self.entry.focus_set())

    def on_undo(self):
        """Annule la dernière main"""
        if not self.hands or self.is_analyzing:
            return

        try:
            # Récupérer dernière main
            last_hand = self.hands.pop()

            # Reconstruire l'état du sabot
            self.sabot_state.reset()

            # Rejouer toutes les mains sauf la dernière
            for hand in self.hands:
                observed = [int(ch) for ch in hand['draw_seq']]
                self.sabot_state.subtract_observed(observed)

            # Mise à jour affichage
            if self.hands:
                self.var_draw.set(self.hands[-1]['draw_seq'])
            else:
                self.var_draw.set("—")

            self._update_display()

            # Supprimer de l'historique
            children = self.tree.get_children()
            if children:
                self.tree.delete(children[-1])

            self.var_msg.set("")

        except Exception as e:
            self.var_msg.set(f"Erreur annulation: {str(e)}")

    def on_reset(self):
        """Réinitialise complètement"""
        if self.is_analyzing:
            self.var_msg.set("Analyse en cours, impossible de réinitialiser")
            return
        self._reset()

    def on_analyze_async(self):
        """Lance l'analyse en arrière-plan"""
        if self.is_analyzing:
            self.var_msg.set("Analyse déjà en cours, veuillez attendre...")
            return

        if not self.sabot_state.observed_sequence:
            self.var_msg.set("Aucune main jouée, impossible d'analyser")
            return

        # Configurer moteur selon UI
        try:
            mode = self.var_mode.get().strip()
            if mode == 'auto':
                # Auto: tente topk_adaptive avec epsilon serré, sinon escalade
                self.engine.mode = 'topk_adaptive'
                self.engine.epsilon = 1e-4
            else:
                self.engine.mode = mode
            # Configuration des paramètres internes
            self.engine.primary_top_k = int(self.var_topk_primary.get())
            self.engine.inner_top_k = int(self.var_topk_inner.get())
            if self.engine.mode == 'topk_adaptive':
                # garder epsilon de l'UI sauf en mode auto où on impose 1e-4
                if mode != 'auto':
                    self.engine.epsilon = float(self.var_epsilon.get())
        except Exception as e:
            self.var_msg.set(f"Paramètres invalides: {e}")
            return

        # Démarrer analyse asynchrone
        self.is_analyzing = True
        self.btn_analyze.config(state='disabled')
        self.var_status.set("🔄 Analyse en cours...")
        self.progress_bar.start(10)

        # Désactiver temporairement le champ de saisie pendant l'analyse
        self.entry.config(state='disabled')

        # Lancer dans un thread séparé
        thread = threading.Thread(target=self._analyze_worker, daemon=True)
        thread.start()

    def _analyze_worker(self):
        """Worker d'analyse dans thread séparé AMÉLIORÉ"""
        try:
            # Configurer robustesse avant analyse
            try:
                self.engine.robust = bool(self.var_robust.get())
                self.engine.robust_budget = int(self.var_robust_budget.get())
            except Exception:
                pass

            # CONFIGURATION DES NOUVELLES AMÉLIORATIONS
            try:
                # Configuration du cache adaptatif
                cache_size = int(self.var_cache_size.get())
                if hasattr(self.engine, '_probability_cache'):
                    self.engine._probability_cache.max_size = cache_size

                # Configuration MCTS
                mcts_iterations = int(self.var_mcts_iterations.get())
                if hasattr(self.engine, 'mcts_engine'):
                    self.engine.mcts_engine.max_iterations = mcts_iterations

                # Configuration algorithmes génétiques
                genetic_pop = int(self.var_genetic_population.get())
                if hasattr(self.engine, 'genetic_optimizer'):
                    self.engine.genetic_optimizer.population_size = genetic_pop

                # EXCLUSION TIE AUTOMATIQUE (OBLIGATOIRE POUR TOUS LES MODES)
                # Pas de configuration nécessaire - toujours activé
                if hasattr(self.engine, 'exclude_tie'):
                    self.engine.exclude_tie = True  # TOUJOURS activé

                # CORRECTION DE BIAIS BANKER/PLAYER
                bias_correction = self.var_bias_correction.get()
                if hasattr(self.engine, 'bias_correction'):
                    self.engine.bias_correction = bias_correction
                elif hasattr(self.engine, 'voter') and hasattr(self.engine.voter, 'bias_correction'):
                    self.engine.voter.bias_correction = bias_correction

                # Les paramètres TOP-K sont déjà configurés dans la section standard du GUI
                # (var_topk_primary et var_topk_inner sont utilisés dans la configuration normale)

                # Auto-optimisation si activée
                if self.var_auto_optimize.get():
                    if hasattr(self.engine, 'optimize_for_performance'):
                        self.engine.optimize_for_performance()

            except Exception as e:
                print(f"Erreur configuration améliorations: {e}")

            # Lancer analyse avec le moteur optimisé
            # Marqueur pour mode auto
            try:
                current_mode = self.var_mode.get().strip()
            except Exception:
                current_mode = 'full_support_exact'
            self.engine._requested_auto = (current_mode == 'auto')
            result = self.engine.analyze_next_hand(self.sabot_state.observed_sequence)

            # Programmer mise à jour UI dans thread principal
            self.after(0, self._update_analysis_results, result)

        except Exception as e:
            # Programmer affichage erreur dans thread principal
            self.after(0, self._update_analysis_error, str(e))

    def _update_analysis_results(self, result):
        """Met à jour l'interface avec les résultats d'analyse"""
        try:
            # Arrêter progression
            self.progress_bar.stop()
            self.is_analyzing = False
            self.btn_analyze.config(state='normal')
            self.entry.config(state='normal')  # Réactiver le champ de saisie

            # Si le core a remonté une erreur structurée, basculer en affichage erreur
            if result.get('error'):
                self._update_analysis_error(result.get('error'))
                return

            # Afficher recommandation
            recommendation = result.get('recommendation', '—')
            confidence = result.get('confidence', 0.0)

            if recommendation != '—':
                color = {'PLAYER': '#0078D4', 'BANKER': '#D13438', 'TIE': '#107C10'}.get(recommendation, '#666666')
                self.lbl_reco.config(text=recommendation, fg=color)
            else:
                self.lbl_reco.config(text="—", fg="#666666")

            # Afficher confiance
            self.var_confidence.set(f"{confidence*100:.1f}%")

            # Afficher votes
            votes = result.get('votes', {})
            if isinstance(votes.get('PLAYER', 0), float):
                votes_text = f"P:{votes.get('PLAYER', 0):.6f} B:{votes.get('BANKER', 0):.6f} T:{votes.get('TIE', 0):.6f}"
            else:
                votes_text = f"P:{votes.get('PLAYER', 0):,} B:{votes.get('BANKER', 0):,} T:{votes.get('TIE', 0):,}"
            self.var_votes.set(votes_text)

            # Afficher métriques AMÉLIORÉES
            metrics = result.get('metrics', {})
            self.var_total_time.set(f"{metrics.get('total_time', 0):.2f}s")
            self.var_memory.set(f"{metrics.get('memory_peak_mb', 0):.0f}MB")
            self.var_analyzed.set(f"{result.get('total_analyzed', 0):,}")

            # NOUVELLE MÉTRIQUE : Cache hit rate
            cache_hit_rate = result.get('cache_hit_rate', 0.0)
            self.var_cache_hit_rate.set(f"{cache_hit_rate*100:.1f}%")

            # MÉTRIQUES SPÉCIALES POUR LES MODES UNIFIÉS
            if result.get('mode') == 'intelligent_unified' and 'unified_analysis' in result:
                unified = result['unified_analysis']
                print(f"=== DÉTAILS INTELLIGENCE UNIFIÉE ===")
                print(f"Stratégie génétique: ratio={unified['genetic_strategy']['top_k_ratio']:.3f}")
                print(f"Améliorations bayésiennes: {unified['bayesian_improvements']:,}")
                print(f"Sélection MCTS: {unified['mcts_selection_ratio']*100:.1f}%")
                print(f"Combinaisons multivariées: {len(unified['multivariate_combinations'])}")

                # Afficher les facteurs de confiance
                factors = unified['confidence_factors']
                print(f"Confiance - Base: {factors['base']*100:.1f}% | Évidence: {factors['evidence']*100:.1f}% | MCTS: {factors['mcts']*100:.1f}% | Multivarié: {factors['multivariate']*100:.1f}%")

            elif result.get('mode') == 'intelligent_unified_fast' and 'fast_analysis' in result:
                unified = result['fast_analysis']
                print(f"=== DÉTAILS INTELLIGENCE UNIFIÉE RAPIDE ===")
                print(f"Stratégie adaptative: ratio={unified['target_ratio']:.3f}")
                print(f"Amélioration bayésienne: facteur={unified['improvement_factor']:.4f}")
                print(f"Bonus évidence: {unified['evidence_bonus']*100:.1f}%")

                # Afficher les paramètres d'analyse
                topk_primaire = self.var_topk_primary.get()  # Utiliser le vrai paramètre
                print(f"Exclusion TIE: AUTOMATIQUE (tous modes)")
                print(f"TOP-K utilisé: {topk_primaire}")
                print(f"Sextuplets analysés: {result.get('total_analyzed', 0):,}")

            # Détail étapes (MMAP optimisé)
            step_times = f"MMAP:{metrics.get('mmap_time', 0):.1f}s Prob:{metrics.get('probability_time', 0):.1f}s Sel:{metrics.get('selection_time', 0):.3f}s Loi:{metrics.get('law_time', 0):.1f}s Vote:{metrics.get('vote_time', 0):.3f}s"
            self.var_step_times.set(step_times)

            # Afficher certificat (si présent) + annotation ergonomique
            cert = result.get('certificate', None)
            if self.var_mode.get() == 'full_support_exact':
                cert = None  # pas de certificat en plein support exact
            cov_text = "—"
            annotation = "—"
            if cert is not None:
                k = cert.get('K')
                eps = cert.get('epsilon')
                R = cert.get('residual_R')
                Mwin = cert.get('Mwin_topK')
                Msec = cert.get('Msec_topK')
                ok = cert.get('certified')
                delta = (Mwin - Msec) if (Mwin is not None and Msec is not None) else 0.0
                # Annotation ergonomique sous la recommandation
                if (delta > (R or 0.0)) and (confidence >= 0.50):
                    annotation = "OK"
                else:
                    annotation = "S'ABSTENIR"
                self.var_reco_note.set(annotation)
                try:
                    self.lbl_reco_note.config(fg="#FFFF00", bg="#000000")
                except Exception:
                    pass
                extra = f" | K={k} R={R:.3e} Δ={delta:.3e}"
                if eps is not None:
                    extra += f" ε={eps}"
                    # Couverture vs K pour l'affichage
                    cov_text = f"Couverture≈{(1.0 - float(eps))*100:.2f}% à K={k}"
                self.var_msg.set((self.var_msg.get() + extra).strip())
            else:
                # Pas de certificat fourni: on peut déduire une annotation conservatrice
                # Si le moteur fournit une annotation explicite (cas du mode max_seq_no_tie_conditional), l'utiliser.
                engine_note = result.get('annotation', None)
                if isinstance(engine_note, str) and engine_note:
                    self.var_reco_note.set(engine_note)
                    try:
                        self.lbl_reco_note.config(fg="#FFFF00", bg="#000000")
                    except Exception:
                        pass
                else:
                    self.var_reco_note.set("S'ABSTENIR" if confidence < 0.50 else "OK")
                    try:
                        self.lbl_reco_note.config(fg="#FFFF00", bg="#000000")
                    except Exception:
                        pass
            self.var_coverage.set(cov_text)

            # Mettre à jour historique si dernière main
            if self.hands:
                children = self.tree.get_children()
                if children:
                    last_item = children[-1]
                    current_values = list(self.tree.item(last_item, 'values'))
                    current_values[2] = f"{recommendation} ({confidence*100:.1f}%)"
                    self.tree.item(last_item, values=current_values)

                # Mettre à jour données main
                self.hands[-1]['recommendation'] = recommendation

            # Incertitude = 1 - confiance
            try:
                self.var_uncertainty.set(f"{(1.0 - float(confidence))*100:.1f}%")
            except Exception:
                self.var_uncertainty.set("—")

            # Afficher robustesse (si fournie par le moteur en plein support exact)
            robust_info = result.get('robust', None)
            if robust_info is not None:
                cl1 = robust_info['confidence_lower'].get(1, 0.0)
                cl2 = robust_info['confidence_lower'].get(2, 0.0)
                stab = robust_info.get('stability_index', '—')
                note = robust_info.get('note', '')
                self.var_robust_info.set(f"L1=1:{cl1:.3f} L1=2:{cl2:.3f} | stabilité: {stab} {note}")
            else:
                self.var_robust_info.set("—")



            self.var_status.set("✅ Analyse terminée - Prêt pour prochaine main")
            base_msg = f"Recommandation: {recommendation} avec {confidence*100:.1f}% de confiance"
            if cert is not None:
                base_msg += f" | Certifié: {'Oui' if ok else 'Non'}"
            self.var_msg.set(base_msg)

            # Remettre automatiquement le focus sur le champ de saisie
            self.after(500, lambda: self.entry.focus_set())

        except Exception as e:
            self._update_analysis_error(f"Erreur mise à jour: {str(e)}")

    def _update_analysis_error(self, error_msg):
        """Affiche une erreur d'analyse"""
        self.progress_bar.stop()
        self.is_analyzing = False
        self.btn_analyze.config(state='normal')
        self.entry.config(state='normal')  # Réactiver le champ de saisie
        self.var_status.set("❌ Erreur")
        self.var_msg.set(f"Erreur analyse: {error_msg}")

        # Remettre le focus sur le champ de saisie
        self.after(100, lambda: self.entry.focus_set())

    # === MÉTHODES DE COMPATIBILITÉ (DEPRECATED) ===

    def compute_exhaustive(self):
        """DEPRECATED: Utiliser on_analyze_async à la place"""
        if hasattr(self, 'initial') and hasattr(self, 'draw_prefix'):
            counts = subtract_observed(self.initial, self.draw_prefix)
            masses, top = enumerate_sextuplets_exhaustive(counts, top_k=50, processes=8)
            # Affichage basique pour compatibilité
            if hasattr(self, 'var_prob_exh'):
                self.var_prob_exh.set(f"P={masses['PLAYER']:.4f} | B={masses['BANKER']:.4f} | T={masses['TIE']:.4f}")

    def on_vote_majoritaire(self):
        """DEPRECATED: Utiliser on_analyze_async à la place"""
        if hasattr(self, 'draw_prefix') and hasattr(self, 'initial'):
            if not self.draw_prefix:
                return
            counts = subtract_observed(self.initial, self.draw_prefix)
            res = majority_vote_topk(counts, K=500000, processes=8)
            if hasattr(self, 'var_vote'):
                if res['total'] <= 0:
                    self.var_vote.set("(aucun)")
                else:
                    self.var_vote.set(f"Reco Top‑K: {res['reco']} ({res['reco_percent']*100:.2f}%) | PLAYER={res['PLAYER']} BANKER={res['BANKER']} TIE={res['TIE']}")

    def _update_mode_ui(self):
        """Met à jour l'interface selon le mode - TOUS LES PARAMÈTRES TOUJOURS ACCESSIBLES"""
        try:
            mode = self.var_mode.get().strip()
        except Exception:
            mode = 'full_support_exact'

        # NOUVELLE LOGIQUE : TOUS LES PARAMÈTRES TOUJOURS ACCESSIBLES
        # Fonction helper pour s'assurer que tous les widgets sont activés
        def ensure_enabled(widget):
            try:
                widget.config(state='normal')
            except Exception:
                pass

        # ACTIVER TOUS LES PARAMÈTRES POUR TOUS LES MODES
        # Epsilon, Top-K primaire, Top-K interne, Robuste P_min, Budget
        essential_widgets = [
            getattr(self, 'ent_eps', None),
            getattr(self, 'ent_topk_primary', None),
            getattr(self, 'ent_topk_inner', None),
            getattr(self, 'chk_robust', None),
            getattr(self, 'ent_robust_budget', None)
        ]

        # S'assurer que TOUS les paramètres essentiels sont TOUJOURS accessibles
        for widget in essential_widgets:
            if widget is not None:
                ensure_enabled(widget)

        # Activer aussi tous les paramètres d'amélioration
        enhancement_widgets = [
            getattr(self, 'var_cache_size', None),
            getattr(self, 'var_mcts_iterations', None),
            getattr(self, 'var_genetic_population', None),
            getattr(self, 'var_auto_optimize', None)
        ]

        # Chercher et activer tous les widgets d'amélioration
        for child in self.winfo_children():
            if hasattr(child, 'winfo_children'):
                for subchild in child.winfo_children():
                    if hasattr(subchild, 'winfo_children'):
                        for widget in subchild.winfo_children():
                            if hasattr(widget, 'config'):
                                try:
                                    widget.config(state='normal')
                                except:
                                    pass

        # MISE À JOUR DES INFORMATIONS SUR LE MODE
        mode_descriptions = {
            'intelligent_unified': "INTELLIGENCE UNIFIÉE: Utilise TOP-K existant + exclusion TIE",
            'intelligent_unified_complete': "INTELLIGENCE UNIFIÉE COMPLÈTE: Tous algorithmes (lent)",
            'full_support_exact': "Analyse exhaustive 1M sextuplets avec cache adaptatif",
            'auto': "Mode automatique avec escalade intelligente",
            'topk_fixed': "TOP-K fixe avec optimisations",
            'topk_adaptive': "TOP-K adaptatif avec certificats",
            'full_certified': "Analyse certifiée complète",
            'max_seq_no_tie': "Séquence optimale sans égalité",
            'max_seq_no_tie_conditional': "Séquence conditionnelle optimale",
            'bayesian_adaptive': "Mise à jour bayésienne dynamique P(H|E)",
            'mcts_exploration': "Exploration intelligente Monte Carlo Tree Search",
            'genetic_optimized': "Optimisation par algorithmes génétiques",
            'multivariate_analysis': "Analyse hypergéométrique multivariée"
        }

        description = mode_descriptions.get(mode, "Mode d'analyse avancé")
        if hasattr(self, 'var_mode_info'):
            self.var_mode_info.set(description)




# =========================
# POINT D'ENTRÉE PRINCIPAL
# =========================

def main():
    """Point d'entrée principal de l'application"""
    try:
        app = App()
        app.mainloop()
    except Exception as e:
        print(f"Erreur fatale: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

