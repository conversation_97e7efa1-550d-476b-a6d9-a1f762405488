#!/usr/bin/env python3
"""
Système de vote amélioré avec méthodes scientifiques d'agrégation de probabilités
Basé sur les meilleures pratiques de la littérature scientifique
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
from collections import Counter
from dataclasses import dataclass
import math

@dataclass
class SextupletWithOutcome:
    """Structure pour un sextuplet avec son outcome et sa probabilité"""
    values: tuple
    probability: float
    outcome: str
    cards_used: int

class BayesianModelAveraging:
    """
    Implémentation du Bayesian Model Averaging (BMA)
    Référence: Hoeting et al. (1999) "Bayesian model averaging: a tutorial"
    """
    
    def __init__(self, models_weights: Optional[List[float]] = None):
        self.models_weights = models_weights
    
    def aggregate(self, predictions: List[Dict[str, float]]) -> Dict[str, float]:
        """
        Agrège les prédictions de plusieurs modèles avec BMA
        
        Args:
            predictions: Liste de dictionnaires {outcome: probability}
        
        Returns:
            Prédiction agrégée
        """
        if not predictions:
            return {'PLAYER': 0.0, 'BANKER': 0.0}
        
        # Poids uniformes si non spécifiés
        if self.models_weights is None:
            weights = [1.0 / len(predictions)] * len(predictions)
        else:
            weights = self.models_weights
        
        # Agrégation pondérée
        aggregated = {'PLAYER': 0.0, 'BANKER': 0.0}
        for pred, weight in zip(predictions, weights):
            for outcome in aggregated:
                if outcome in pred:
                    aggregated[outcome] += weight * pred[outcome]
        
        # Normalisation
        total = sum(aggregated.values())
        if total > 0:
            aggregated = {k: v/total for k, v in aggregated.items()}
        
        return aggregated

class IsotonicCalibration:
    """
    Calibration isotonique des probabilités
    Référence: Zadrozny & Elkan (2002) "Transforming classifier scores into accurate multiclass probability estimates"
    """
    
    def __init__(self, alpha: float = 0.3):
        self.alpha = alpha  # Facteur de lissage
    
    def calibrate_probabilities(self, sextuplets: List[SextupletWithOutcome]) -> List[SextupletWithOutcome]:
        """
        Calibre les probabilités pour équilibrer les masses
        
        Args:
            sextuplets: Liste des sextuplets avec probabilités
        
        Returns:
            Sextuplets avec probabilités calibrées
        """
        if not sextuplets:
            return sextuplets
        
        # Calculer les masses par outcome
        masses = {'PLAYER': 0.0, 'BANKER': 0.0}
        for s in sextuplets:
            masses[s.outcome] += s.probability
        
        total_mass = sum(masses.values())
        if total_mass == 0:
            return sextuplets
        
        # Facteurs de calibration pour équilibrer à 50/50
        target_ratio = 0.5
        calibration_factors = {}
        for outcome in masses:
            current_ratio = masses[outcome] / total_mass
            if current_ratio > 0:
                # Calibration douce avec facteur alpha
                raw_factor = target_ratio / current_ratio
                calibration_factors[outcome] = self.alpha * raw_factor + (1 - self.alpha) * 1.0
            else:
                calibration_factors[outcome] = 1.0
        
        # Appliquer la calibration
        calibrated_sextuplets = []
        for s in sextuplets:
            calibrated_prob = s.probability * calibration_factors[s.outcome]
            calibrated_sextuplets.append(
                SextupletWithOutcome(s.values, calibrated_prob, s.outcome, s.cards_used)
            )
        
        return calibrated_sextuplets

class PlattScaling:
    """
    Calibration par Platt Scaling (transformation sigmoïde)
    Référence: Platt (1999) "Probabilistic outputs for support vector machines"
    """
    
    def __init__(self, A: float = -1.0, B: float = 0.0):
        self.A = A  # Paramètre de pente
        self.B = B  # Paramètre de décalage
    
    def sigmoid(self, x: float) -> float:
        """Fonction sigmoïde stable numériquement"""
        if x >= 0:
            exp_neg_x = math.exp(-x)
            return 1 / (1 + exp_neg_x)
        else:
            exp_x = math.exp(x)
            return exp_x / (1 + exp_x)
    
    def calibrate_probability(self, prob: float) -> float:
        """Calibre une probabilité individuelle"""
        if prob <= 0:
            return 0.0
        if prob >= 1:
            return 1.0
        
        # Transformation logit puis sigmoïde
        logit = math.log(prob / (1 - prob))
        calibrated_logit = self.A * logit + self.B
        return self.sigmoid(calibrated_logit)

class EnsembleVotingSystem:
    """
    Système de vote d'ensemble avec multiple méthodes d'agrégation
    """
    
    def __init__(self, method: str = 'bma'):
        """
        Args:
            method: Méthode d'agrégation ('bma', 'isotonic', 'platt', 'log_weighted', 'threshold')
        """
        self.method = method
        self.bma = BayesianModelAveraging()
        self.isotonic = IsotonicCalibration(alpha=0.3)
        self.platt = PlattScaling(A=-0.5, B=0.1)
    
    def vote_bayesian_model_averaging(self, sextuplets: List[SextupletWithOutcome]) -> Dict[str, object]:
        """Vote avec Bayesian Model Averaging"""
        if not sextuplets:
            return self._empty_result()
        
        # Créer plusieurs "modèles" avec différentes pondérations
        models_predictions = []
        
        # Modèle 1: Probabilités originales
        mass1 = {'PLAYER': 0.0, 'BANKER': 0.0}
        for s in sextuplets:
            mass1[s.outcome] += s.probability
        total1 = sum(mass1.values())
        if total1 > 0:
            models_predictions.append({k: v/total1 for k, v in mass1.items()})
        
        # Modèle 2: Probabilités avec correction logarithmique
        mass2 = {'PLAYER': 0.0, 'BANKER': 0.0}
        for s in sextuplets:
            log_prob = math.log(1 + s.probability)
            mass2[s.outcome] += log_prob
        total2 = sum(mass2.values())
        if total2 > 0:
            models_predictions.append({k: v/total2 for k, v in mass2.items()})
        
        # Modèle 3: Probabilités avec seuillage
        threshold = np.percentile([s.probability for s in sextuplets], 90)
        mass3 = {'PLAYER': 0.0, 'BANKER': 0.0}
        for s in sextuplets:
            capped_prob = min(s.probability, threshold)
            mass3[s.outcome] += capped_prob
        total3 = sum(mass3.values())
        if total3 > 0:
            models_predictions.append({k: v/total3 for k, v in mass3.items()})
        
        # Agrégation BMA
        if models_predictions:
            aggregated = self.bma.aggregate(models_predictions)
            winner = max(aggregated, key=aggregated.get)
            confidence = aggregated[winner]
            
            return {
                'PLAYER': aggregated.get('PLAYER', 0.0),
                'BANKER': aggregated.get('BANKER', 0.0),
                'TIE': 0.0,
                'total': len(sextuplets),
                'recommendation': winner,
                'confidence': confidence
            }
        
        return self._empty_result()
    
    def vote_isotonic_calibration(self, sextuplets: List[SextupletWithOutcome]) -> Dict[str, object]:
        """Vote avec calibration isotonique"""
        if not sextuplets:
            return self._empty_result()
        
        # Calibrer les probabilités
        calibrated_sextuplets = self.isotonic.calibrate_probabilities(sextuplets)
        
        # Vote pondéré standard sur les probabilités calibrées
        mass = {'PLAYER': 0.0, 'BANKER': 0.0}
        for s in calibrated_sextuplets:
            mass[s.outcome] += s.probability
        
        total_mass = sum(mass.values())
        if total_mass > 0:
            normalized_mass = {k: v/total_mass for k, v in mass.items()}
            winner = max(normalized_mass, key=normalized_mass.get)
            confidence = normalized_mass[winner]
            
            return {
                'PLAYER': normalized_mass.get('PLAYER', 0.0),
                'BANKER': normalized_mass.get('BANKER', 0.0),
                'TIE': 0.0,
                'total': len(sextuplets),
                'recommendation': winner,
                'confidence': confidence
            }
        
        return self._empty_result()
    
    def vote_platt_scaling(self, sextuplets: List[SextupletWithOutcome]) -> Dict[str, object]:
        """Vote avec Platt Scaling"""
        if not sextuplets:
            return self._empty_result()
        
        # Calibrer chaque probabilité individuellement
        mass = {'PLAYER': 0.0, 'BANKER': 0.0}
        for s in sextuplets:
            calibrated_prob = self.platt.calibrate_probability(s.probability)
            mass[s.outcome] += calibrated_prob
        
        total_mass = sum(mass.values())
        if total_mass > 0:
            normalized_mass = {k: v/total_mass for k, v in mass.items()}
            winner = max(normalized_mass, key=normalized_mass.get)
            confidence = normalized_mass[winner]
            
            return {
                'PLAYER': normalized_mass.get('PLAYER', 0.0),
                'BANKER': normalized_mass.get('BANKER', 0.0),
                'TIE': 0.0,
                'total': len(sextuplets),
                'recommendation': winner,
                'confidence': confidence
            }
        
        return self._empty_result()
    
    def vote(self, sextuplets: List[SextupletWithOutcome]) -> Dict[str, object]:
        """
        Vote principal avec la méthode sélectionnée
        
        Args:
            sextuplets: Liste des sextuplets avec outcomes
        
        Returns:
            Résultat du vote
        """
        if self.method == 'bma':
            return self.vote_bayesian_model_averaging(sextuplets)
        elif self.method == 'isotonic':
            return self.vote_isotonic_calibration(sextuplets)
        elif self.method == 'platt':
            return self.vote_platt_scaling(sextuplets)
        else:
            # Fallback vers vote pondéré standard
            return self._vote_weighted_standard(sextuplets)
    
    def _vote_weighted_standard(self, sextuplets: List[SextupletWithOutcome]) -> Dict[str, object]:
        """Vote pondéré standard (fallback)"""
        if not sextuplets:
            return self._empty_result()
        
        mass = {'PLAYER': 0.0, 'BANKER': 0.0}
        for s in sextuplets:
            mass[s.outcome] += s.probability
        
        total_mass = sum(mass.values())
        if total_mass > 0:
            winner = max(mass, key=mass.get)
            confidence = mass[winner] / total_mass
            
            return {
                'PLAYER': mass['PLAYER'],
                'BANKER': mass['BANKER'],
                'TIE': 0.0,
                'total': len(sextuplets),
                'recommendation': winner,
                'confidence': confidence
            }
        
        return self._empty_result()
    
    def _empty_result(self) -> Dict[str, object]:
        """Résultat vide par défaut"""
        return {
            'PLAYER': 0.0,
            'BANKER': 0.0,
            'TIE': 0.0,
            'total': 0,
            'recommendation': '—',
            'confidence': 0.0
        }
