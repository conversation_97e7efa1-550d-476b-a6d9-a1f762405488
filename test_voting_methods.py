#!/usr/bin/env python3
"""
Test différentes méthodes de vote et d'agrégation de probabilités
pour identifier le problème de sur-correction
"""

import sys
import os
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.getcwd())

try:
    from baccarat_exhaustive_core import (
        MajorityVoter, 
        SextupletWithOutcome,
        get_base_rate_correction_factors,
        get_outcomes_distribution
    )
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def create_test_data(banker_bias: float = 0.55) -> List[SextupletWithOutcome]:
    """Crée des données de test avec un biais contrôlé"""
    test_data = []
    
    # Simuler 1000 sextuplets avec un biais vers BANKER
    for i in range(1000):
        if np.random.random() < banker_bias:
            outcome = 'BANKER'
        else:
            outcome = 'PLAYER'
        
        # Probabilité aléatoire
        prob = np.random.uniform(0.001, 0.01)
        sextuplet = tuple(np.random.randint(0, 10, 6))
        
        test_data.append(SextupletWithOutcome(sextuplet, prob, outcome, 6))
    
    return test_data

def test_current_voting_system():
    """Test le système de vote actuel"""
    print("=== TEST DU SYSTÈME DE VOTE ACTUEL ===\n")
    
    # Créer des données avec différents niveaux de biais
    bias_levels = [0.51, 0.52, 0.55, 0.60]
    
    for bias in bias_levels:
        print(f"Test avec biais BANKER = {bias:.1%}")
        test_data = create_test_data(bias)
        
        # Compter les outcomes réels
        actual_counts = Counter(s.outcome for s in test_data)
        actual_banker_ratio = actual_counts['BANKER'] / len(test_data)
        
        print(f"  Données réelles: BANKER={actual_banker_ratio:.1%}, PLAYER={1-actual_banker_ratio:.1%}")
        
        # Test avec correction
        voter = MajorityVoter()
        voter.bias_correction = True
        result_with_correction = voter.vote_weighted(test_data)
        
        # Test sans correction
        voter.bias_correction = False
        result_no_correction = voter.vote_weighted(test_data)
        
        print(f"  Sans correction: {result_no_correction['recommendation']}")
        print(f"  Avec correction: {result_with_correction['recommendation']}")
        print()

def test_bayesian_model_averaging():
    """Test d'une approche Bayesian Model Averaging"""
    print("=== TEST BAYESIAN MODEL AVERAGING ===\n")
    
    test_data = create_test_data(0.507)  # Biais léger comme dans les vraies données
    
    # Simuler plusieurs "modèles" avec des probabilités différentes
    models_predictions = []
    
    for s in test_data:
        # Modèle 1: probabilités originales
        model1_prob = s.probability
        
        # Modèle 2: probabilités ajustées par facteur de correction
        correction_factors = get_base_rate_correction_factors()
        if s.outcome == 'BANKER':
            model2_prob = s.probability * correction_factors['BANKER']
        else:
            model2_prob = s.probability * correction_factors['PLAYER']
        
        # Modèle 3: probabilités avec correction modérée (racine carrée)
        if s.outcome == 'BANKER':
            model3_prob = s.probability * np.sqrt(correction_factors['BANKER'])
        else:
            model3_prob = s.probability * np.sqrt(correction_factors['PLAYER'])
        
        models_predictions.append({
            'outcome': s.outcome,
            'model1': model1_prob,
            'model2': model2_prob,
            'model3': model3_prob
        })
    
    # Agrégation BMA avec poids égaux
    weights = [1/3, 1/3, 1/3]
    
    mass = {'PLAYER': 0.0, 'BANKER': 0.0}
    for pred in models_predictions:
        bma_prob = (weights[0] * pred['model1'] + 
                   weights[1] * pred['model2'] + 
                   weights[2] * pred['model3'])
        mass[pred['outcome']] += bma_prob
    
    total_mass = mass['PLAYER'] + mass['BANKER']
    normalized_mass = {k: v/total_mass for k, v in mass.items()}
    winner = max(normalized_mass, key=normalized_mass.get)
    
    print(f"BMA Result: {winner}")
    print(f"  PLAYER: {normalized_mass['PLAYER']:.3f}")
    print(f"  BANKER: {normalized_mass['BANKER']:.3f}")
    print()

def test_isotonic_regression_approach():
    """Test d'une approche de calibration isotonique simplifiée"""
    print("=== TEST CALIBRATION ISOTONIQUE ===\n")
    
    test_data = create_test_data(0.507)
    
    # Séparer par outcome
    banker_probs = [s.probability for s in test_data if s.outcome == 'BANKER']
    player_probs = [s.probability for s in test_data if s.outcome == 'PLAYER']
    
    # Calibration simple: ajuster les probabilités pour équilibrer les masses
    banker_mass = sum(banker_probs)
    player_mass = sum(player_probs)
    total_mass = banker_mass + player_mass
    
    # Facteurs de calibration pour équilibrer à 50/50
    banker_calibration = 0.5 / (banker_mass / total_mass)
    player_calibration = 0.5 / (player_mass / total_mass)
    
    print(f"Facteurs de calibration:")
    print(f"  BANKER: {banker_calibration:.6f}")
    print(f"  PLAYER: {player_calibration:.6f}")
    
    # Appliquer la calibration
    calibrated_mass = {'PLAYER': 0.0, 'BANKER': 0.0}
    for s in test_data:
        if s.outcome == 'BANKER':
            calibrated_prob = s.probability * banker_calibration
        else:
            calibrated_prob = s.probability * player_calibration
        calibrated_mass[s.outcome] += calibrated_prob
    
    total_calibrated = calibrated_mass['PLAYER'] + calibrated_mass['BANKER']
    normalized_calibrated = {k: v/total_calibrated for k, v in calibrated_mass.items()}
    winner = max(normalized_calibrated, key=normalized_calibrated.get)
    
    print(f"Résultat calibré: {winner}")
    print(f"  PLAYER: {normalized_calibrated['PLAYER']:.3f}")
    print(f"  BANKER: {normalized_calibrated['BANKER']:.3f}")
    print()

def test_weighted_ensemble_voting():
    """Test d'un vote d'ensemble pondéré avec différentes stratégies"""
    print("=== TEST VOTE D'ENSEMBLE PONDÉRÉ ===\n")
    
    test_data = create_test_data(0.507)
    
    # Stratégie 1: Vote simple (baseline)
    simple_votes = Counter(s.outcome for s in test_data)
    simple_winner = max(simple_votes, key=simple_votes.get)
    print(f"Vote simple: {simple_winner} ({simple_votes})")
    
    # Stratégie 2: Vote pondéré par probabilité (actuel)
    prob_mass = {'PLAYER': 0.0, 'BANKER': 0.0}
    for s in test_data:
        prob_mass[s.outcome] += s.probability
    prob_winner = max(prob_mass, key=prob_mass.get)
    print(f"Vote pondéré par probabilité: {prob_winner}")
    
    # Stratégie 3: Vote pondéré avec correction logarithmique
    log_mass = {'PLAYER': 0.0, 'BANKER': 0.0}
    for s in test_data:
        # Utiliser log(1 + prob) pour réduire l'impact des grandes probabilités
        log_prob = np.log(1 + s.probability)
        log_mass[s.outcome] += log_prob
    log_winner = max(log_mass, key=log_mass.get)
    print(f"Vote avec correction logarithmique: {log_winner}")
    
    # Stratégie 4: Vote avec seuillage des probabilités
    threshold = np.percentile([s.probability for s in test_data], 90)
    thresh_mass = {'PLAYER': 0.0, 'BANKER': 0.0}
    for s in test_data:
        # Plafonner les probabilités très élevées
        capped_prob = min(s.probability, threshold)
        thresh_mass[s.outcome] += capped_prob
    thresh_winner = max(thresh_mass, key=thresh_mass.get)
    print(f"Vote avec seuillage (seuil={threshold:.6f}): {thresh_winner}")
    print()

if __name__ == "__main__":
    try:
        print("ANALYSE DES MÉTHODES DE VOTE ET D'AGRÉGATION\n")
        print("=" * 60)
        
        # Analyser d'abord la distribution réelle
        print("Distribution réelle des outcomes:")
        distribution = get_outcomes_distribution()
        correction_factors = get_base_rate_correction_factors()
        print()
        
        # Tester différentes approches
        test_current_voting_system()
        test_bayesian_model_averaging()
        test_isotonic_regression_approach()
        test_weighted_ensemble_voting()
        
        print("=== CONCLUSIONS ===")
        print("1. Le problème n'est pas une sur-correction majeure")
        print("2. Les facteurs de correction sont très modérés")
        print("3. Le problème pourrait être dans l'agrégation des probabilités")
        print("4. Solutions recommandées:")
        print("   - Bayesian Model Averaging avec poids adaptatifs")
        print("   - Calibration isotonique des probabilités")
        print("   - Vote pondéré avec correction logarithmique")
        print("   - Seuillage des probabilités extrêmes")
        
    except Exception as e:
        print(f"Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
